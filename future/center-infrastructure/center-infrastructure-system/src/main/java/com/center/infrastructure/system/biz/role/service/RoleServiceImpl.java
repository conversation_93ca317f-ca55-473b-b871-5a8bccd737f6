package com.center.infrastructure.system.biz.role.service;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.Orika.OrikaUtils;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuModel;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuRepository;
import com.center.infrastructure.system.biz.role.persistence.RoleModel;
import com.center.infrastructure.system.biz.role.persistence.RoleRepository;
import com.center.infrastructure.system.biz.role.pojo.RoleCreateReq;
import com.center.infrastructure.system.biz.role.pojo.RoleMenuReq;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.role.pojo.RoleUpdateReq;
import com.center.infrastructure.system.biz.user.persistence.UserRoleRepository;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import jakarta.annotation.Resource;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

@Service
public class RoleServiceImpl implements RoleService {

  @Resource
  private RoleRepository roleRepository;

  @Resource
  private RoleMenuRepository roleMenuRepository;

  @Resource
  private UserRoleRepository userRoleRepository;

  @Override
  public void save(RoleCreateReq roleCreateReq) {
    checkNameAndCode(roleCreateReq.getName(),roleCreateReq.getCode());
    roleRepository.save(OrikaUtils.convert(roleCreateReq, RoleModel.class));
  }

  @Override
  public void update(RoleUpdateReq roleUpdateReq) {
    checkNameAndCode(roleUpdateReq.getName(),roleUpdateReq.getCode(),roleUpdateReq.getId());
    roleRepository.save(OrikaUtils.convert(roleUpdateReq,RoleModel.class));
  }

  @Override
  public RoleResp get(Long id) {
    return OrikaUtils.convert(getRoleModel(id),RoleResp.class);
  }

  @Override
  public void delete(Long id) {
    try {
//      删除前需要检查此角色下是否有用户
      if(userRoleRepository.countByRoleId(id)>0){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY,"角色下面存在用户");
      }
      roleRepository.deleteById(id);
    }catch (EmptyResultDataAccessException e){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"角色不存在");
    }
  }

  @Override
  public void saveRoleMenu(RoleMenuReq roleMenuReq) {
    getRoleModel(roleMenuReq.getRoleId());
    roleMenuRepository.deleteByRoleId(roleMenuReq.getRoleId());
    List<RoleMenuModel> listRoleMenu = new LinkedList<>();
    Iterator<Long> it = roleMenuReq.getListMenuId().iterator();
    while (it.hasNext()){
      RoleMenuModel roleMenuModel = new RoleMenuModel();
      roleMenuModel.setMenuId(it.next());
      roleMenuModel.setRoleId(roleMenuReq.getRoleId());
      listRoleMenu.add(roleMenuModel);
    }
    roleMenuRepository.saveAll(listRoleMenu);
  }

  private RoleModel getRoleModel(Long id){
    Optional<RoleModel> optionalRoleModel = roleRepository.findById(id);
    if(optionalRoleModel.isPresent()){
      return optionalRoleModel.get();
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"角色不存在");
    }
  }

  private void checkNameAndCode(String name,String code,Long id){
    RoleModel roleModel = getRoleModel(id);
    if(!name.equals(roleModel.getName())){
      roleModel = roleRepository.findByName(name);
      if(null != roleModel){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"重复的角色名称");
      }
      roleModel = roleRepository.findByCode(code);
      if(null != roleModel){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"重复的角色代码");
      }
    }
  }

  private void checkNameAndCode(String name,String code){
    RoleModel roleModel = roleRepository.findByNameOrCode(name,code);
    if(null != roleModel){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"重复的角色名称或代码");
    }
  }


}
