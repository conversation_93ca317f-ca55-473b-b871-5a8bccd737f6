package com.center.infrastructure.system.biz.dict.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.infrastructure.system.biz.dict.enumeration.DictTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class BaseDictData {

    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String dictLabel;

    @Schema(description = "字典数据值")
    private String dictValue;

    @Schema(description = "字典类型值")
    @NotNull(message = "字典类型不能为空")
    @EnumConvert(value = DictTypeEnum.class,srcFieldName = "dictTypeEnum")
    private DictTypeEnum dictType;

    @Schema(description = "字典类型ID")
    private Long dictTypeEnum;

    @Schema(description = "是否默认项")
    private Integer isDefault;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "父级id")
    private Long parentId;

    @Schema(description = "备注")
    private String remark;
}
