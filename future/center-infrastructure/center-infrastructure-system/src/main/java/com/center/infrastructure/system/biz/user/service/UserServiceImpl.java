package com.center.infrastructure.system.biz.user.service;

import cn.hutool.crypto.SecureUtil;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.Orika.OrikaUtils;
import com.center.infrastructure.system.biz.role.persistence.RoleRepository;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.service.TenantService;
import com.center.infrastructure.system.biz.user.mapper.UserMapper;
import com.center.infrastructure.system.biz.user.persistence.UserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.center.infrastructure.system.biz.user.persistence.UserRoleModel;
import com.center.infrastructure.system.biz.user.persistence.UserRoleRepository;
import com.center.infrastructure.system.biz.user.pojo.UserCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserResp;
import com.center.infrastructure.system.biz.user.pojo.UserRoleCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserRoleUpdateReq;
import com.center.infrastructure.system.biz.user.pojo.UserUpdateReq;
import java.time.LocalDateTime;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserServiceImpl implements UserService {

  @Resource
  private UserRepository userRepository;

  @Resource
  private UserRoleRepository userRoleRepository;

  @Resource
  private RoleRepository roleRepository;

  @Resource
  private TenantService tenantService;

  @Override
  public UserResp get(Long id) {
//    return OrikaUtils.convert(getUserModel(id),UserResp.class);
    return UserMapper.INSTANCE.toUserResp(getUserModel(id));
  }

  @Override
  public void save(UserCreateReq userCreateReq) {
    checkTenant(userCreateReq.getTenantId());
    checkUsername(userCreateReq.getUsername(),userCreateReq.getTenantId());
    UserModel userModel = OrikaUtils.convert(userCreateReq,UserModel.class);
    userModel.setPassword(userCreateReq.getMD5Password());
    userRepository.save(userModel);
  }

  @Override
  public void update(UserUpdateReq userUpdateReq) {
    UserModel originalUserModel = getUserModel(userUpdateReq.getId());
    checkUsername(originalUserModel,userUpdateReq.getUsername(),userUpdateReq.getTenantId());
    if(CommonStatusEnum.INACTIVE.getValue().equals(originalUserModel.getStatus())
        &&CommonStatusEnum.ACTIVE.getValue().equals(userUpdateReq.getStatus())){
      checkTenant(userUpdateReq.getTenantId());
    }
    UserModel userModel = OrikaUtils.convert(userUpdateReq,UserModel.class);
    userModel.setPassword(userUpdateReq.getMD5Password());
    userRepository.save(userModel);
  }

  @Override
  public void delete(Long id) {
    try{
      userRepository.deleteById(id);
    }catch (EmptyResultDataAccessException e){
      log.error("删除节点出错！",e);
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在");
    }
  }

  @Override
  public void saveUserRole(UserRoleCreateReq userRoleCreateReq) {
//    检查UserID是否存在
    getUserModel(userRoleCreateReq.getUserId());
    if(!roleRepository.findById(userRoleCreateReq.getRoleId()).isPresent()){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"此角色不存在");
    }
    userRoleRepository.save(OrikaUtils.convert(userRoleCreateReq, UserRoleModel.class));
  }

  @Override
  public void updateUserRole(UserRoleUpdateReq userRoleUpdateReq) {
    getUserModel(userRoleUpdateReq.getUserId());
    if(!roleRepository.findById(userRoleUpdateReq.getRoleId()).isPresent()){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"此角色不存在");
    }
    userRoleRepository.save(OrikaUtils.convert(userRoleUpdateReq,UserRoleModel.class));
  }

  @Override
  public UserResp getByUsernameAndPassword(String username, String password) {
    UserModel userModel = userRepository.findByUsernameAndPassword(username, SecureUtil.md5(password));
    if(null != userModel){
      return OrikaUtils.convert(userModel,UserResp.class);
    }
    return null;
  }

  @Override
  public void updateUserLoginInfo(Long id, String ip) {
    if(StringUtils.isEmpty(ip)){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY,"IP地址不能为空");
    }
    UserModel userModel = getUserModel(id);
    userModel.setLoginIp(ip);
    userModel.setLoginTime(LocalDateTime.now());
    userRepository.save(userModel);
  }


  private UserModel getUserModel(Long id){
    Optional<UserModel> option = userRepository.findById(id);
    if(option.isPresent()){
      return option.get();
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"此用户不存在");
    }
  }

  private void checkTenant(Long tenantId){
    TenantResp tenantResp = tenantService.get(tenantId);
    if(tenantResp == null){
      throw ServiceExceptionUtil
          .exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在");
    }
    if(LocalDateTime.now().isAfter(tenantResp.getExpireTime())){
      throw ServiceExceptionUtil
          .exception(GlobalErrorCodeConstants.INACTIVE_OBJECT, "租户已失效");
    }
    Long accountCount = userRepository.countByTenantIdAndStatus(tenantId, CommonStatusEnum.ACTIVE);
    if(accountCount >= tenantResp.getAccountCount()){
      throw ServiceExceptionUtil
          .exception(GlobalErrorCodeConstants.OVER_LIMIT, "租户下的激活用户数量超过最大限制");
    }
  }


  private void checkUsername(String username,Long tenantId){
      UserModel userModel = userRepository.findByUsernameAndTenantId(username,tenantId);
      if(userModel != null){
        throw ServiceExceptionUtil
            .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的用户名");
      }
  }

  private void checkUsername(UserModel userModel,String username,Long tenantId){
    if(null != userModel){
      if(!username.equals(userModel.getUsername())){
        userModel = userRepository.findByUsernameAndTenantId(username,tenantId);
        if(userModel != null){
          throw ServiceExceptionUtil
              .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的用户名");
        }
      }
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在");
    }
  }
}
