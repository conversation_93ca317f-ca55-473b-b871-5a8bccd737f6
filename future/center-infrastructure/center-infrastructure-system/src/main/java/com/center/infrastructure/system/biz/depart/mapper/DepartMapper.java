package com.center.infrastructure.system.biz.depart.mapper;

import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.pojo.DepartCreateReq;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.depart.pojo.DepartUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DepartMapper {

    DepartMapper INSTANCE = Mappers.getMapper(DepartMapper.class);

    DepartModel toDepartModel(DepartCreateReq departCreateReq);

    DepartModel toDepartModel(DepartUpdateReq departUpdateReq);

    DepartResp toDepartResp(DepartModel departModel);

    List<DepartResp> toDepartRespList(List<DepartModel> departModelList);
}
