package com.center.infrastructure.system.biz.dict.service;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.Orika.OrikaUtils;
import com.center.infrastructure.system.biz.dict.persistence.DictTypeModel;
import com.center.infrastructure.system.biz.dict.persistence.DictTypeRepository;
import com.center.infrastructure.system.biz.dict.pojo.DictCreateTypeReq;
import com.center.infrastructure.system.biz.dict.pojo.DictTypeResp;
import com.center.infrastructure.system.biz.dict.pojo.DictUpdateTypeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DictTypeServiceImpl implements DictTypeService {
    @Resource
    private DictTypeRepository dictTypeRepository;

    @Override
    public void createType(DictCreateTypeReq dictCreateTypeReq) {
        checkDictType(dictCreateTypeReq.getDictName(), dictCreateTypeReq.getDictType());
        DictTypeModel dictTypeModel = OrikaUtils.convert(dictCreateTypeReq, DictTypeModel.class);
        dictTypeModel.setStatus(CommonStatusEnum.ACTIVE);
        dictTypeRepository.save(dictTypeModel);
    }

    @Override
    public void updateType(DictUpdateTypeReq dictUpdateTypeReq) {
        checkUpdateType(dictUpdateTypeReq);
        dictTypeRepository.save(OrikaUtils.convert(dictUpdateTypeReq, DictTypeModel.class));
    }

    @Override
    public List<DictTypeResp> selectType() {
        return dictTypeRepository.findAll().stream()
                .map(dictTypeModel -> OrikaUtils.convert(dictTypeModel, DictTypeResp.class))
                .collect(Collectors.toList());
    }

    //检测字典类型是否重名
    public void checkDictType(String dictName, String dictType) {
        if (dictTypeRepository.existsByDictNameOrDictType(dictName, dictType)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "字典类型已存在");
        }
    }

    public void checkUpdateType(DictUpdateTypeReq dictUpdateTypeReq) {
        DictTypeModel dictTypeModel = dictTypeRepository.findById(dictUpdateTypeReq.getId()).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "当前字典类型不存在"));
        if (dictTypeModel.getDictType().equals(dictUpdateTypeReq.getDictType())) {
            if (!dictTypeModel.getDictName().equals(dictUpdateTypeReq.getDictName())) {
                if (dictTypeRepository.existsByDictName(dictUpdateTypeReq.getDictName())) {
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "字典名称已存在");
                };
            }
        }else {
            if (dictTypeRepository.existsByDictType(dictUpdateTypeReq.getDictType())) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "字典类型已存在");
            }
        }
    }
}
