package com.center.infrastructure.system.biz.dict.mapper;

import com.center.infrastructure.system.biz.dict.persistence.DictDataModel;
import com.center.infrastructure.system.biz.dict.persistence.DictTypeModel;
import com.center.infrastructure.system.biz.dict.pojo.DictCreateDataReq;
import com.center.infrastructure.system.biz.dict.pojo.DictDataResp;
import com.center.infrastructure.system.biz.dict.pojo.DictTypeResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DictMapper {

    DictMapper INSTANCE = Mappers.getMapper(DictMapper.class);

    DictDataModel toDictDataModel(DictCreateDataReq dictCreateDataReq);

    DictDataResp toDictDataResp(DictDataModel dictDataModel);

    DictTypeResp toDictTypeResp(DictTypeModel dictTypeModel);

    List<DictTypeResp> toDictTypeRespList(List<DictTypeModel> dictTypeModelList);
}
