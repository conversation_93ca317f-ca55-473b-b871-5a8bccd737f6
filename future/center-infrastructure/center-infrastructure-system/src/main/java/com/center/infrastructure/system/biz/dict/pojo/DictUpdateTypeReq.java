package com.center.infrastructure.system.biz.dict.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class DictUpdateTypeReq {
    @Schema(description = "字典类型id")
    @NotNull(message = "字典类型id不能为空")
    private Long id;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "字典类型名")
    private String dictName;

    @Schema(description = "字典数据状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "字典类型描述")
    private String remark;
}
