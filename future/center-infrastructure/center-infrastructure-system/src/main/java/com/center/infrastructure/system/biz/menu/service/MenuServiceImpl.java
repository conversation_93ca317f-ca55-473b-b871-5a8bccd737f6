package com.center.infrastructure.system.biz.menu.service;

import static com.center.infrastructure.system.common.Constant.MENU_ROOT_ID;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.infrastructure.system.biz.menu.mapper.MenuMapper;
import com.center.infrastructure.system.biz.menu.enumerate.MenuCategoryEnum;
import com.center.infrastructure.system.biz.menu.persistence.MenuModel;
import com.center.infrastructure.system.biz.menu.persistence.MenuRepository;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuRepository;
import com.center.infrastructure.system.common.ErrorCodeConstant;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MenuServiceImpl implements MenuService {

  @Resource
  private MenuRepository menuRepository;

  @Resource
  private RoleMenuRepository roleMenuRepository;

  @Override
  public void save(MenuCreateReq menuCreateReq) {
    checkParent(menuCreateReq.getParentId());
    menuRepository.save(OrikaUtils.convert(menuCreateReq, MenuModel.class));
  }

  @Override
  public void update(MenuUpdateReq menuUpdateReq) {
    checkParent(menuUpdateReq.getParentId());
    getMenuModel(menuUpdateReq.getId());
    menuRepository.save(OrikaUtils.convert(menuUpdateReq,MenuModel.class));
  }

  @Override
  public void delete(Long id) {
    MenuModel menuModel = getMenuModel(id);
    if (menuRepository.countByParentId(menuModel.getId()) > 0) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除此菜单下的所有菜单");
    }
    if(roleMenuRepository.countByMenuId(id)>0){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除此菜单分配给的用户");
    }
    menuRepository.deleteById(id);
  }

  @Override
  public MenuResp get(Long id) {
    return MenuMapper.INSTANCE.toMenuResp(getMenuModel(id));
  }

  @Override
  public List<Tree<String>> getAllMenu() {
    List<MenuModel> menuModelList = menuRepository.findAll();
    return getMenuTree(menuModelList);
  }

  private MenuModel getMenuModel(Long id){
    Optional<MenuModel> optionalMenuModel = menuRepository.findById(id);
    if(optionalMenuModel.isPresent()){
      return optionalMenuModel.get();
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"菜单不存在");
    }
  }

  private void checkParent(Long parentId){
    if(parentId == MENU_ROOT_ID){
      return;
    }
    Optional<MenuModel> optionalMenuModel = menuRepository.findById(parentId);
    if(!optionalMenuModel.isPresent()){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"菜单父节点");
    }else if(optionalMenuModel.get().getCategory().equals(MenuCategoryEnum.BUTTON)){
      throw ServiceExceptionUtil.exception(ErrorCodeConstant.MENU_NOT_BELONG_TO_BUTTON);
    }
  }


  /**
   * 生成树型结构的菜单树
   * @param menuModelList
   * @return
   */
  private  List<Tree<String>> getMenuTree(List<MenuModel> menuModelList) {
    List<TreeNode> nodeList = CollUtil.newArrayList();
    Iterator<MenuModel> iterator = menuModelList.iterator();
    while (iterator.hasNext()) {
      MenuModel menuModel = iterator.next();
      TreeNode<String> treeNode = new TreeNode(menuModel.getId(), menuModel.getParentId(),
          menuModel.getName(), menuModel.getSort());
      // 如果还需要给树形添加其他字段，返回给前端，需使用map进行封装
      HashMap<String, Object> hashMap = new HashMap<>();
      hashMap.put("path", menuModel.getPath());
      hashMap.put("category", menuModel.getCategory());
      hashMap.put("icon", menuModel.getIcon());
      hashMap.put("sort", menuModel.getSort());
      treeNode.setExtra(hashMap);
      nodeList.add(treeNode);
    }

    //配置
    TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
    //设置权重对应的名称,用于排序
    treeNodeConfig.setWeightKey("sort");
    treeNodeConfig.setIdKey("id");
// 最大递归深度
    treeNodeConfig.setDeep(3);//这个是设置树形结构的层级
//转换器 (含义:找出父节点为字符串零的所有子节点, 并递归查找对应的子节点, 深度最多为 3)
    // 0表示最顶层的id是0,即最高的父级id为多少
    List<Tree<String>> build = TreeUtil.<TreeNode, String>build(nodeList,
        String.valueOf(MENU_ROOT_ID), treeNodeConfig,
        (treeNode, tree) -> {
          tree.setId(String.valueOf(treeNode.getId()));
          tree.setParentId(String.valueOf(treeNode.getParentId()));
          tree.setName(treeNode.getName());
          tree.putAll(treeNode.getExtra());
        });

    return build;
  }
}
