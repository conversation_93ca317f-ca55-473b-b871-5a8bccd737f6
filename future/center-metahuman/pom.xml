<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.center</groupId>
        <artifactId>center</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>center-metahuman</artifactId>
    <packaging>jar</packaging>


    <dependencies><dependency>
        <groupId>com.center</groupId>
        <artifactId>center-framework-storage-factory</artifactId>
        <version>${revision}</version>
    </dependency>

        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-cache-factory</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-db</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-web</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-chat</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-log</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-infrastructure-system</artifactId>
            <version>${revision}</version>
        </dependency>


        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <classifier>jakarta</classifier>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <scope>provided</scope>
            <classifier>jakarta</classifier>
            <version>5.1.0</version>
        </dependency>
    </dependencies>
</project>