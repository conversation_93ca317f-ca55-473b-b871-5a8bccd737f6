package com.center.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

//  默认 Controller，用于测试。

@RestController
public class DefaultController {

  @GetMapping
  @RequestMapping(value = "/hello", method = RequestMethod.GET)
  @Operation(description = "服务启动验证接口")
  public String defaultController() {
    return "Hello World!";
  }
}
